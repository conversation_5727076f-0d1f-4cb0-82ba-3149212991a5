---
- name: Test SSH connections for lab environment
  hosts: all
  gather_facts: false
  vars:
    ssh_users:
      - ubuntu
      - cloud-user
      - centos
    ssh_keys:
      - controller.pem
      - labms.pem
  tasks:
    - name: Test all user/key combinations
      block:
        - name: "Test {{ item.0 }} with {{ item.1 }}"
          ansible.builtin.ping:
          vars:
            ansible_user: "{{ item.0 }}"
            ansible_ssh_private_key_file: "/runner/.ssh/{{ item.1 }}"
            ansible_ssh_common_args: "-o ConnectTimeout=5 -o StrictHostKeyChecking=no"
          register: test_result
          ignore_errors: true

        - name: "Log test result for {{ item.0 }}/{{ item.1 }}"
          debug:
            msg: |
              Host: {{ inventory_hostname }}
              User: {{ item.0 }}
              Key: {{ item.1 }}
              Result: {{ 'SUCCESS' if test_result is succeeded else 'FAILED' }}
              {% if test_result is failed %}
              Error: {{ test_result.msg | default('Unknown error') }}
              {% endif %}

      loop: "{{ ssh_users | product(ssh_keys) | list }}"

    - name: Test passwordless connections
      block:
        - name: "Test passwordless {{ item }}"
          ansible.builtin.ping:
          vars:
            ansible_user: "{{ item }}"
            ansible_ssh_private_key_file: ""
            ansible_ssh_common_args: "-o ConnectTimeout=5 -o StrictHostKeyChecking=no -o PasswordAuthentication=no"
          register: passwordless_result
          ignore_errors: true

        - name: "Log passwordless test result for {{ item }}"
          debug:
            msg: |
              Host: {{ inventory_hostname }}
              User: {{ item }}
              Key: passwordless
              Result: {{ 'SUCCESS' if passwordless_result is succeeded else 'FAILED' }}
              {% if passwordless_result is failed %}
              Error: {{ passwordless_result.msg | default('Unknown error') }}
              {% endif %}

      loop: "{{ ssh_users }}"

    - name: Generate connection test summary
      debug:
        msg: |
          Connection test completed for {{ inventory_hostname }}
          Check the logs above for detailed results.
          
          Next steps:
          1. Review successful connections
          2. Run dynamic-ssh-connection.yml to establish working connection
          3. Use the working connection for further automation
