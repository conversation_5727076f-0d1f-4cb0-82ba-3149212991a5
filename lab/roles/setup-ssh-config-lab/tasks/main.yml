---
- name: Ensure .ssh directory exists
  file:
    path: /runner/.ssh/
    state: directory
    mode: '0700'

- name: Copy SSH keys for lab environment
  copy:
    src: "{{ item }}"
    dest: "/runner/.ssh/{{ item }}"
    mode: '0400'
  loop:
    - controller.pem
    - labms.pem
  delegate_to: localhost
  run_once: true

- name: Create SSH config for lab environment
  blockinfile:
    path: /runner/.ssh/config
    create: yes
    mode: '0600'
    marker: "# {mark} LAB ENVIRONMENT SSH CONFIG"
    block: |
      # Lab environment SSH configuration
      Host *
        # Primary keys for lab
        IdentityFile /runner/.ssh/controller.pem
        IdentityFile /runner/.ssh/labms.pem
        
        # SSH connection settings
        StrictHostKeyChecking no
        UserKnownHostsFile /dev/null
        ConnectTimeout 10
        ServerAliveInterval 60
        ServerAliveCountMax 3
        
        # Try multiple authentication methods
        PasswordAuthentication no
        PubkeyAuthentication yes
        IdentitiesOnly yes
        
        # Logging for debugging
        LogLevel ERROR

- name: Verify SSH keys are readable
  stat:
    path: "/runner/.ssh/{{ item }}"
  register: key_stats
  loop:
    - controller.pem
    - labms.pem

- name: Display SSH key status
  debug:
    msg: |
      SSH Key Status for Lab Environment:
      {% for result in key_stats.results %}
      - {{ result.item }}: {{ 'OK' if result.stat.exists else 'MISSING' }} 
        (Mode: {{ result.stat.mode if result.stat.exists else 'N/A' }})
      {% endfor %}

- name: Fail if any SSH keys are missing
  fail:
    msg: "Missing SSH key: {{ item.item }}"
  when: not item.stat.exists
  loop: "{{ key_stats.results }}"
