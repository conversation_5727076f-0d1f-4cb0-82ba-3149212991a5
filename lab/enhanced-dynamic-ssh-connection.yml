---
- name: Setup SSH configuration for lab environment
  hosts: localhost
  become: false
  gather_facts: false
  vars:
    ssh_keys:
      - controller.pem
      - labms.pem
    ssh_users:
      - ubuntu
      - cloud-user
      - centos
  roles:
    - setup-ssh-config-lab

- name: Enhanced dynamic SSH user detection with OpenStack metadata
  hosts: all
  gather_facts: false
  vars:
    ssh_users:
      - ubuntu
      - cloud-user
      - centos
    ssh_keys:
      - controller.pem
      - labms.pem
    # OS to user mapping for intelligent detection
    os_user_mapping:
      ubuntu: ubuntu
      redhat: cloud-user
      centos: centos
      rhel: cloud-user
      rocky: cloud-user
      almalinux: cloud-user
    connection_cache: {}
  tasks:
    - name: Initialize connection tracking
      set_fact:
        successful_connections: {}
        failed_connections: {}
        detected_os: null
        preferred_user: null

    - name: Try to detect OS from OpenStack metadata (if available)
      block:
        - name: Get OpenStack metadata
          uri:
            url: "http://***************/openstack/latest/meta_data.json"
            method: GET
            timeout: 5
          register: openstack_metadata
          delegate_to: "{{ inventory_hostname }}"

        - name: Parse OS information from metadata
          set_fact:
            detected_os: "{{ openstack_metadata.json.meta.os_distro | default('unknown') | lower }}"
            preferred_user: "{{ os_user_mapping[openstack_metadata.json.meta.os_distro | default('unknown') | lower] | default(null) }}"
          when: 
            - openstack_metadata is succeeded
            - openstack_metadata.json.meta is defined
            - openstack_metadata.json.meta.os_distro is defined

        - name: Display detected OS information
          debug:
            msg: |
              OpenStack metadata detected:
              - OS Distribution: {{ detected_os }}
              - Preferred User: {{ preferred_user | default('not determined') }}
          when: detected_os is defined and detected_os != 'unknown'

      rescue:
        - name: OpenStack metadata not available
          debug:
            msg: "OpenStack metadata not available, will try all users"
            verbosity: 1

    - name: Try preferred user first (if detected from metadata)
      include_tasks: tasks/try-ssh-connection.yml
      vars:
        target_user: "{{ preferred_user }}"
        ssh_key: "{{ item }}"
      loop: "{{ ssh_keys }}"
      when: 
        - preferred_user is defined
        - preferred_user is not none
        - inventory_hostname not in successful_connections

    - name: Try remaining users if preferred user failed
      include_tasks: tasks/try-ssh-connection.yml
      vars:
        target_user: "{{ item.0 }}"
        ssh_key: "{{ item.1 }}"
      loop: "{{ (ssh_users | difference([preferred_user]) if preferred_user else ssh_users) | product(ssh_keys) | list }}"
      when: inventory_hostname not in successful_connections

    - name: Fallback to passwordless authentication
      include_tasks: tasks/try-passwordless-connection.yml
      vars:
        target_user: "{{ item }}"
      loop: "{{ ssh_users }}"
      when: inventory_hostname not in successful_connections

    - name: Gather system facts and validate OS detection
      block:
        - name: Get system information
          ansible.builtin.setup:
          register: system_facts

        - name: Store connection details with OS validation
          set_fact:
            connection_info:
              user: "{{ ansible_user }}"
              os_family: "{{ ansible_facts['os_family'] }}"
              distribution: "{{ ansible_facts['distribution'] }}"
              distribution_version: "{{ ansible_facts['distribution_version'] }}"
              hostname: "{{ ansible_facts['hostname'] }}"
              fqdn: "{{ ansible_facts['fqdn'] }}"
              architecture: "{{ ansible_facts['architecture'] }}"
              ssh_key_used: "{{ ssh_key_used | default('passwordless') }}"
              detected_os: "{{ detected_os | default('not_detected') }}"
              os_detection_accurate: "{{ (detected_os | lower) in (ansible_facts['distribution'] | lower) if detected_os else false }}"

        - name: Display comprehensive connection summary
          debug:
            msg: |
              Successfully connected to {{ inventory_hostname }}:
              - User: {{ connection_info.user }}
              - OS: {{ connection_info.distribution }} {{ connection_info.distribution_version }}
              - Architecture: {{ connection_info.architecture }}
              - SSH Key: {{ connection_info.ssh_key_used }}
              - FQDN: {{ connection_info.fqdn }}
              - Detected OS (metadata): {{ connection_info.detected_os }}
              - OS Detection Accurate: {{ connection_info.os_detection_accurate }}

        - name: Save enhanced connection details to file
          copy:
            content: |
              # Enhanced connection details for {{ inventory_hostname }}
              # Generated on {{ ansible_date_time.iso8601 }}
              
              # Connection Information
              ansible_user: {{ connection_info.user }}
              ansible_ssh_private_key_file: /runner/.ssh/{{ connection_info.ssh_key_used }}
              
              # OS Information
              os_family: {{ connection_info.os_family }}
              distribution: {{ connection_info.distribution }}
              distribution_version: {{ connection_info.distribution_version }}
              architecture: {{ connection_info.architecture }}
              
              # Detection Information
              detected_os_from_metadata: {{ connection_info.detected_os }}
              os_detection_accurate: {{ connection_info.os_detection_accurate }}
              
              # System Information
              hostname: {{ connection_info.hostname }}
              fqdn: {{ connection_info.fqdn }}
            dest: "/tmp/{{ inventory_hostname }}_enhanced_connection_info.yml"
          delegate_to: localhost

      rescue:
        - name: Connection failed for all methods
          fail:
            msg: |
              Failed to establish SSH connection to {{ inventory_hostname }} using any of the following methods:
              - Users tried: {{ ssh_users | join(', ') }}
              - SSH keys tried: {{ ssh_keys | join(', ') }}
              - Passwordless authentication: attempted
              - Detected OS: {{ detected_os | default('unknown') }}
              - Preferred user: {{ preferred_user | default('none') }}
              
              Please verify:
              1. Host is reachable
              2. SSH service is running
              3. Correct SSH keys are available
              4. User accounts exist on target system
