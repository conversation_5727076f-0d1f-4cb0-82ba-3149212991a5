# 🚀 Quick Start Guide

Get up and running with dynamic SSH connections in 5 minutes!

## 📋 Prerequisites

- Ansible 2.9+ installed
- SSH access to target hosts
- SSH private keys available

## ⚡ Quick Setup

### 1. Prepare SSH Keys

**For Lab Environment:**
```bash
# Copy your SSH keys to the lab role
cp /path/to/your/controller.pem lab/roles/setup-ssh-config-lab/files/
cp /path/to/your/labms.pem lab/roles/setup-ssh-config-lab/files/

# Set proper permissions
chmod 400 lab/roles/setup-ssh-config-lab/files/*.pem
```

**For Production Environment:**
```bash
# Copy your SSH key to the production role
cp /path/to/your/devops.pem production/roles/setup-ssh-config-production/files/

# Set proper permissions
chmod 400 production/roles/setup-ssh-config-production/files/*.pem
```

### 2. Update Inventory

Edit the inventory file for your environment:

**Lab:** `lab/inventory/hosts.yml`
```yaml
all:
  children:
    lab_servers:
      hosts:
        your-host-01:
          ansible_host: ************
        your-host-02:
          ansible_host: ************
```

**Production:** `production/inventory/hosts.yml`
```yaml
all:
  children:
    production_servers:
      hosts:
        your-prod-host-01:
          ansible_host: *********
```

### 3. Test Connection

**Lab Environment:**
```bash
# Test all connection methods
ansible-playbook -i lab/inventory/hosts.yml lab/test-connections.yml

# Run dynamic connection
ansible-playbook -i lab/inventory/hosts.yml lab/dynamic-ssh-connection.yml
```

**Production Environment:**
```bash
# Run dynamic connection
ansible-playbook -i production/inventory/hosts.yml production/dynamic-ssh-connection.yml
```

## 🎯 Common Use Cases

### Single Host Testing
```bash
# Test specific host
ansible-playbook -i lab/inventory/hosts.yml lab/dynamic-ssh-connection.yml --limit your-host-01
```

### Group Targeting
```bash
# Target web servers only
ansible-playbook -i lab/inventory/hosts.yml lab/dynamic-ssh-connection.yml --limit lab_web_servers
```

### Enhanced Detection (Lab Only)
```bash
# Use OpenStack metadata detection
ansible-playbook -i lab/inventory/hosts.yml lab/enhanced-dynamic-ssh-connection.yml
```

## 📊 Expected Output

### Successful Connection
```
TASK [Log successful connection] ***
ok: [your-host-01] => {
    "msg": "✓ Successfully connected to your-host-01 as ubuntu using controller.pem"
}

TASK [Display connection summary] ***
ok: [your-host-01] => {
    "msg": "Successfully connected to your-host-01:\n- User: ubuntu\n- OS: Ubuntu 20.04\n- Architecture: x86_64\n- SSH Key: controller.pem\n- FQDN: your-host-01.example.com\n"
}
```

### Generated Files
Check `/tmp/` for connection details:
```bash
ls -la /tmp/*_connection_info.yml
cat /tmp/your-host-01_connection_info.yml
```

## 🔧 Troubleshooting

### Issue: SSH Key Not Found
```
fatal: [localhost]: FAILED! => {"msg": "Could not find or access 'controller.pem'"}
```
**Fix:** Ensure keys are in the correct location with proper permissions

### Issue: All Connections Failed
```
Failed to establish SSH connection using any of the following methods
```
**Fix:** 
1. Verify host is reachable: `ping your-host-01`
2. Check SSH service: `telnet your-host-01 22`
3. Verify user accounts exist

### Issue: Permission Denied
```
Permission denied (publickey)
```
**Fix:**
1. Check key permissions: `ls -la *.pem`
2. Verify public key is on target host
3. Test manual SSH: `ssh -i controller.pem ubuntu@your-host-01`

## 🎉 Next Steps

Once connections are working:

1. **Save Connection Details**: Use generated files for future automation
2. **Create Host Groups**: Organize hosts by function/environment
3. **Build Workflows**: Chain this playbook with your application playbooks
4. **Set Up AWX**: Configure in AWX for team collaboration

## 💡 Pro Tips

- **Start with test-connections.yml** to understand what works
- **Use --check mode** for dry runs: `ansible-playbook --check ...`
- **Enable verbose output** for debugging: `ansible-playbook -vvv ...`
- **Keep keys secure** and rotate regularly
- **Document successful patterns** for your infrastructure

## 📞 Need Help?

1. Check the full [README.md](README.md) for detailed documentation
2. Review [AWX Configuration Guide](awx-configuration.md) for AWX setup
3. Enable debug mode with `-vvv` for detailed logs
4. Test individual components with the test playbook

---

**Happy Automating! 🤖**
