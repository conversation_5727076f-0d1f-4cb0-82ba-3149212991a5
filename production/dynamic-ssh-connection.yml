---
- name: Setup SSH configuration for production environment
  hosts: localhost
  become: false
  gather_facts: false
  vars:
    ssh_keys:
      - devops.pem
    ssh_users:
      - ubuntu
      - cloud-user
      - centos
  roles:
    - setup-ssh-config-production

- name: Dynamic SSH user detection and connection for production
  hosts: all
  gather_facts: false
  vars:
    ssh_users:
      - ubuntu
      - cloud-user
      - centos
    ssh_keys:
      - devops.pem
    connection_cache: {}
  tasks:
    - name: Initialize connection tracking
      set_fact:
        successful_connections: {}
        failed_connections: {}

    - name: Attempt connection with different users and keys
      include_tasks: tasks/try-ssh-connection.yml
      vars:
        target_user: "{{ item.0 }}"
        ssh_key: "{{ item.1 }}"
      loop: "{{ ssh_users | product(ssh_keys) | list }}"
      when: inventory_hostname not in successful_connections

    - name: Fallback to passwordless authentication
      include_tasks: tasks/try-passwordless-connection.yml
      vars:
        target_user: "{{ item }}"
      loop: "{{ ssh_users }}"
      when: inventory_hostname not in successful_connections

    - name: Gather system facts with successful connection
      block:
        - name: Get system information
          ansible.builtin.setup:
          register: system_facts

        - name: Store connection details
          set_fact:
            connection_info:
              user: "{{ ansible_user }}"
              os_family: "{{ ansible_facts['os_family'] }}"
              distribution: "{{ ansible_facts['distribution'] }}"
              distribution_version: "{{ ansible_facts['distribution_version'] }}"
              hostname: "{{ ansible_facts['hostname'] }}"
              fqdn: "{{ ansible_facts['fqdn'] }}"
              architecture: "{{ ansible_facts['architecture'] }}"
              ssh_key_used: "{{ ssh_key_used | default('passwordless') }}"

        - name: Display connection summary
          debug:
            msg: |
              Successfully connected to {{ inventory_hostname }}:
              - User: {{ connection_info.user }}
              - OS: {{ connection_info.distribution }} {{ connection_info.distribution_version }}
              - Architecture: {{ connection_info.architecture }}
              - SSH Key: {{ connection_info.ssh_key_used }}
              - FQDN: {{ connection_info.fqdn }}

        - name: Save connection details to file
          copy:
            content: |
              # Connection details for {{ inventory_hostname }}
              # Generated on {{ ansible_date_time.iso8601 }}
              ansible_user: {{ connection_info.user }}
              ansible_ssh_private_key_file: /runner/.ssh/{{ connection_info.ssh_key_used }}
              # OS Information
              os_family: {{ connection_info.os_family }}
              distribution: {{ connection_info.distribution }}
              distribution_version: {{ connection_info.distribution_version }}
              architecture: {{ connection_info.architecture }}
            dest: "/tmp/{{ inventory_hostname }}_connection_info.yml"
          delegate_to: localhost

      rescue:
        - name: Connection failed for all methods
          fail:
            msg: |
              Failed to establish SSH connection to {{ inventory_hostname }} using any of the following methods:
              - Users tried: {{ ssh_users | join(', ') }}
              - SSH keys tried: {{ ssh_keys | join(', ') }}
              - Passwordless authentication: attempted
              
              Please verify:
              1. Host is reachable
              2. SSH service is running
              3. Correct SSH keys are available
              4. User accounts exist on target system
